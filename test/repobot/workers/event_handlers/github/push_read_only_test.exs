defmodule Repobot.Workers.EventHandlers.GitHub.PushReadOnlyTest do
  use Repobot.DataCase, async: true

  import Mox
  import Repobot.Test.Fixtures

  alias Repobot.{Events, SourceFiles, SourceFile, Repository}
  alias Repobot.Workers.EventHandlers.GitHub.Push

  setup :verify_on_exit!
  setup :set_mox_from_context

  describe "push event handling with read-only source files" do
    setup do
      user = create_user(%{login: "test_user", email: "<EMAIL>"})
      organization = user.default_organization

      # Create a template repository
      template_repo =
        %Repository{
          id: Ecto.UUID.generate(),
          name: "template-repo",
          owner: "test-owner",
          full_name: "test-owner/template-repo",
          template: true,
          organization_id: organization.id,
          data: %{"default_branch" => "main"}
        }
        |> Repo.insert!()

      # Create a read-only source file from template repository
      read_only_source_file =
        %SourceFile{
          id: Ecto.UUID.generate(),
          name: "config.yml",
          content: "# Original template config",
          target_path: "config.yml",
          read_only: true,
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: organization.id
        }
        |> Repo.insert!()

      %{
        user: user,
        organization: organization,
        template_repo: template_repo,
        read_only_source_file: read_only_source_file
      }
    end

    test "can update read-only source file content from template repository push", %{
      template_repo: template_repo,
      read_only_source_file: source_file,
      organization: organization,
      user: user
    } do
      # Mock GitHub API to return updated content
      Repobot.GitHub.MockAPI
      |> expect(:client, fn _user -> :mock_client end)
      |> expect(:get_file_content, fn _client, "test-owner", "template-repo", "config.yml", "abc123" ->
        {:ok, "# Updated template config from push", %{"sha" => "def456"}}
      end)

      # Create a push event for the template repository
      push_payload = %{
        "repository" => %{
          "id" => template_repo.id,
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "ref" => "refs/heads/main",
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update config.yml",
            "added" => [],
            "modified" => ["config.yml"],
            "removed" => []
          }
        ]
      }

      {:ok, event} =
        Events.create_event(%{
          type: "github.push",
          payload: push_payload,
          organization_id: organization.id,
          user_id: user.id,
          repository_id: template_repo.id
        })

      # Process the push event
      assert {:ok, _result} = Push.handle(event)

      # Verify that the read-only source file was updated
      updated_source_file = Repo.get!(SourceFile, source_file.id)
      assert updated_source_file.content == "# Updated template config from push"
      assert updated_source_file.read_only == true  # Should remain read-only
    end

    test "regular update_source_file still prevents read-only updates", %{
      read_only_source_file: source_file
    } do
      # Verify that the regular update function still prevents updates
      result = SourceFiles.update_source_file(source_file, %{"content" => "Should not work"})

      assert {:error, message} = result
      assert message =~ "Cannot update read-only source file"
    end
  end
end
