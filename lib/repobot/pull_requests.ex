defmodule Repobot.PullRequests do
  @moduledoc """
  The PullRequests context.
  """

  import Ecto.Query, warn: false
  alias <PERSON>obot.Repo

  alias Repobot.PullRequest

  @doc """
  Returns a pull request matching the given options.
  """
  def get_pull_request_by(opts) do
    Repo.get_by(PullRequest, opts)
  end

  @doc """
  Creates a pull request with the given attributes.
  """
  def create_pull_request(attrs) do
    %PullRequest{}
    |> PullRequest.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Creates an external pull request (created outside of Repobot) with the given attributes.
  External pull requests don't have an associated source file.
  """
  def create_external_pull_request(attrs) do
    %PullRequest{}
    |> PullRequest.external_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a pull request with the given attributes.
  """
  def update_pull_request(%PullRequest{} = pull_request, attrs) do
    pull_request
    |> PullRequest.changeset(attrs)
    |> Repo.update()
  end
end
