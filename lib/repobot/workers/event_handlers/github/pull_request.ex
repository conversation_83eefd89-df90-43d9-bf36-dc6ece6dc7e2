defmodule Repobot.Workers.EventHandlers.GitHub.PullRequest do
  @moduledoc """
  Oban worker for handling GitHub pull request webhook events.

  This worker processes pull request events that have been stored
  in the events table and updates pull request status in the database.
  """

  use Repobot.Workers.EventHandler, max_attempts: 3

  alias Repobot.{Repositories, PullRequests}

  defmodule PullRequestEvent do
    @moduledoc """
    Represents a GitHub pull request event with processed attributes for easier handling.
    """
    defstruct [
      :repository_full_name,
      :repository_id,
      :organization_id,
      :number,
      :title,
      :html_url,
      :action,
      :merged,
      :branch_name,
      :pull_request
    ]

    @type t :: %__MODULE__{
            repository_full_name: String.t(),
            repository_id: binary() | nil,
            organization_id: binary() | nil,
            number: integer(),
            title: String.t(),
            html_url: String.t(),
            action: String.t(),
            merged: boolean(),
            branch_name: String.t(),
            pull_request: Repobot.PullRequest.t() | nil
          }

    @doc """
    Creates a new PullRequestEvent struct from a GitHub webhook payload.
    """
    def new(payload) do
      repository_full_name = payload["repository"]["full_name"]
      repository = Repositories.get_repository_by(full_name: repository_full_name)

      %__MODULE__{
        repository_full_name: repository_full_name,
        repository_id: repository && repository.id,
        organization_id: repository && repository.organization_id,
        number: payload["pull_request"]["number"],
        title: payload["pull_request"]["title"],
        html_url: payload["pull_request"]["html_url"],
        action: payload["action"],
        merged: payload["pull_request"]["merged"],
        pull_request: nil
      }
    end

    @doc """
    Loads the associated pull request from the database.
    Returns {:ok, pull_request_event} if found, {:ok, nil} if not found.
    """
    def load_pull_request(%__MODULE__{repository_full_name: repo, number: number} = pr_event) do
      case PullRequests.get_pull_request_by(
             repository: repo,
             pull_request_number: number
           ) do
        nil ->
          Logger.info("Pull request not found: #{repo}##{number}")
          {:ok, %{pr_event | pull_request: nil}}

        pull_request ->
          {:ok, %{pr_event | pull_request: pull_request}}
      end
    end

    @doc """
    Creates a pull request record for PRs that don't exist in the database yet.
    This handles both Repobot-created PRs that failed to create records and external PRs.
    Returns {:ok, pull_request_event} if successful, {:error, reason} if failed.
    """
    def create_pull_request_record(%__MODULE__{} = pr_event) do
      attrs = %{
        repository: pr_event.repository_full_name,
        branch_name: extract_branch_name_from_payload(pr_event),
        pull_request_number: pr_event.number,
        pull_request_url: pr_event.html_url,
        status: "open"
      }

      case PullRequests.create_pull_request(attrs) do
        {:ok, pull_request} ->
          Logger.info("Pull request record created",
            repository: pr_event.repository_full_name,
            pr_number: pr_event.number,
            pull_request_id: pull_request.id
          )

          {:ok, %{pr_event | pull_request: pull_request}}

        {:error, changeset} ->
          Logger.error("Failed to create pull request record",
            repository: pr_event.repository_full_name,
            pr_number: pr_event.number,
            errors: inspect(changeset.errors)
          )

          {:error, "Failed to create pull request record"}
      end
    end

    # Extract branch name from the pull request event
    # For now, we'll use a placeholder since we don't have the branch name in the current payload
    defp extract_branch_name_from_payload(_pr_event) do
      "external-pr-#{DateTime.utc_now() |> DateTime.to_unix()}"
    end

    @doc """
    Determines the new status based on GitHub action and merged state.
    Returns the new status or nil if no change is needed.
    """
    def get_status(%__MODULE__{action: action, merged: merged, pull_request: pull_request}) do
      current_status = pull_request && pull_request.status

      case {action, merged, current_status} do
        # Pull request was closed and merged
        {"closed", true, status} when status != "merged" ->
          "merged"

        # Pull request was closed but not merged
        {"closed", false, status} when status != "closed" ->
          "closed"

        # Pull request was reopened
        {"reopened", _, status} when status != "open" ->
          "open"

        # No status change needed for other actions or if already in correct status
        _ ->
          nil
      end
    end
  end

  @impl true
  def handle(%Events.Event{} = event) do
    pr_event = PullRequestEvent.new(event.payload)

    Logger.info("Processing pull request event",
      event_id: event.id,
      repository: pr_event.repository_full_name,
      pr_number: pr_event.number,
      action: pr_event.action,
      merged: pr_event.merged
    )

    # Check if repository exists
    if pr_event.repository_id do
      with {:ok, pr_event} <- PullRequestEvent.load_pull_request(pr_event) do
        case pr_event.pull_request do
          nil when pr_event.action == "opened" ->
            # Pull request doesn't exist in database and action is "opened" - create it
            case PullRequestEvent.create_pull_request_record(pr_event) do
              {:ok, _updated_pr_event} ->
                Logger.info("Created pull request record for opened PR",
                  event_id: event.id,
                  repository: pr_event.repository_full_name,
                  pr_number: pr_event.number
                )

                :ok

              {:error, reason} ->
                {:error, reason}
            end

          nil ->
            # Pull request doesn't exist in database but action is not "opened" - skip
            Logger.info("Pull request not found in database, skipping non-opened action",
              event_id: event.id,
              repository: pr_event.repository_full_name,
              pr_number: pr_event.number,
              action: pr_event.action
            )

            :ok

          _pull_request ->
            # Pull request exists - check if status update is needed
            case PullRequestEvent.get_status(pr_event) do
              nil ->
                Logger.info("No status change needed for pull request",
                  event_id: event.id,
                  repository: pr_event.repository_full_name,
                  pr_number: pr_event.number,
                  action: pr_event.action
                )

                :ok

              status ->
                update_pull_request_status(event, pr_event, status)
            end
        end
      else
        {:error, reason} ->
          {:error, reason}
      end
    else
      Logger.info("Repository not found for pull request event",
        event_id: event.id,
        repository: pr_event.repository_full_name,
        pr_number: pr_event.number
      )

      :ok
    end
  end

  # Update pull request status in the database
  defp update_pull_request_status(event, %PullRequestEvent{pull_request: nil}, _new_status) do
    Logger.info("Pull request not found in database, skipping status update",
      event_id: event.id
    )

    :ok
  end

  defp update_pull_request_status(event, pr_event, new_status) do
    pull_request = pr_event.pull_request

    case PullRequests.update_pull_request(pull_request, %{status: new_status}) do
      {:ok, updated_pr} ->
        Logger.info("Pull request status updated successfully",
          event_id: event.id,
          pull_request_id: updated_pr.id,
          repository: updated_pr.repository,
          pr_number: updated_pr.pull_request_number,
          old_status: pull_request.status,
          new_status: new_status
        )

        :ok

      {:error, changeset} ->
        Logger.error("Failed to update pull request status",
          event_id: event.id,
          pull_request_id: pull_request.id,
          repository: pull_request.repository,
          pr_number: pull_request.pull_request_number,
          new_status: new_status,
          errors: inspect(changeset.errors)
        )

        {:error, "Failed to update pull request status"}
    end
  end
end
